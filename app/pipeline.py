import os
import sys
import logging
import pathlib
import subprocess

from app.utils.logger import Logger
from app.audio.tts import synthesize
from app.video.face_swap import face_swap
from app.video.montage import generate_video
from app.utils.io import load_spec, ensure_dir
from app.video.compress import normalize_and_compress
from app.video.talking_head import generate_talking_head
from app.video.assemble import write_srt, burn_subtitles_and_watermark, concat_videos_ffmpeg


def run_project(spec_path, out_path, workdir):
    spec = load_spec(spec_path)
    ensure_dir(workdir)
    tmp = pathlib.Path(workdir)
    os.environ['PROJECT_PATH'] = workdir
    pipeline_logger = Logger(logger_name="progress", loglevel=logging.INFO)
    pipeline_logger.info(f"Starting project {spec_path}")
    
    fps = spec.get("output", {}).get("fps", 25)
    res = spec.get("output", {}).get("resolution", "960x540")
    width, height = [int(x) for x in res.lower().split("x")]
    target_size_mb = spec.get("output", {}).get("target_size_mb", 3)
    watermark = spec.get("watermark", "")

    scene_mp4s = []
    for scene in spec["scenes"]:
        pipeline_logger.info(f"Processing scene {scene['id']}")
        sid = scene["id"]
        mode = scene.get("mode", "narration")
        script_text = scene.get("script_text", {})
        voice = scene.get("voice", {})
        speakers = voice.get("speakers", {})

        # 1) Audio
        audio = []
        for person_key, text in script_text.items():
            pipeline_logger.info(f"Generating audio for {person_key}")
            speaker = speakers.get(person_key, None)
            audio_file = str(tmp / f"{sid}_{person_key}.wav")
            audio.append(synthesize(text, audio_file, engine=voice.get("engine","piper"), voice=speaker))

        # 2) Video per mode
        raw_mp4 = str(tmp / f"{sid}_raw.mp4")
        if mode == "narration":
            pipeline_logger.info(f"Generating montage for {sid}")
            images = scene.get("images", [])
            video_engine = scene.get("video_engine", "montage")
            prompt = scene["prompt"] if scene.get("prompt") else None
            raw_mp4 = generate_video(images, raw_mp4, prompt, audio[0], size=res, model=video_engine)
        elif mode == "talking_head":
            pipeline_logger.info(f"Generating talking head for {sid}")
            portrait = scene["portrait"]
            prompt = scene["prompt"] if scene.get("prompt") else None
            engine = scene.get("lipsync_engine", "wav2lip")
            if scene.get("face_swap")==True:
                target = scene.get("target")
                portrait = face_swap(portrait, target)
            generate_talking_head(portrait, audio, raw_mp4, prompt, engine=engine, target_width=width, target_height=height)
        else:
            raise ValueError(f"Unknown scene mode: {mode}")

        # 3) Subtitles + watermark (optional)
        final_scene = raw_mp4
        if spec.get("subtitles", False) or watermark:
            srt = None
            if spec.get("subtitles", False):
                srt = str(tmp / f"{sid}.srt")
                write_srt(script_text, audio[0], srt)
            burned = str(tmp / f"{sid}_burned.mp4")
            burn_subtitles_and_watermark(raw_mp4, srt, watermark, burned)
            final_scene = burned

        scene_mp4s.append(final_scene)

    # 4) Concat scenes
    merged = str(tmp / "merged.mp4")
    concat_videos_ffmpeg(scene_mp4s, merged, target_width=width, target_height=height)

    # 5) Compress to target
    pipeline_logger.info(f"Compressing to {target_size_mb} MB")
    normalize_and_compress(merged, out_path, width=width, height=height, fps=fps, target_size_mb=target_size_mb, two_pass=True)

    pipeline_logger.info(f"[DONE] Wrote {out_path}")
    return out_path
