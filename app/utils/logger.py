import os
import logging


class Logger:
    def __init__(self, loglevel=logging.INFO, logger_name="progress"):
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(loglevel)
        self.ch = logging.StreamHandler()
        self.ch.setLevel(loglevel)
        self.formatter = logging.Formatter("[%(asctime)s] [%(levelname)s] %(message)s", datefmt="%H:%M:%S")
        self.ch.setFormatter(self.formatter)

        log_path = os.path.join(os.getenv("PROJECT_PATH"), "generation.log")
        self.fh = logging.FileHandler(log_path, mode='a')
        self.fh.setLevel(logging.DEBUG)
        self.fh.setFormatter(self.formatter)

        if not self.logger.hasHandlers():
            self.logger.addHandler(self.ch)
            self.logger.addHandler(self.fh)

    def __getattr__(self, name):
        return getattr(self.logger, name)