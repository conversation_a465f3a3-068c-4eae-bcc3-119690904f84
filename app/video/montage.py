import os
import sys
import cv2
import math
import subprocess
import numpy as np

from app.video.assemble import audio_duration_sec

def ken_burns_clip(images, out_path:str, audio_file:str, fps=25, size=(960,540)):
    """
    Simple pan/zoom montage from a list of image paths.
    - images: list of image file paths
    - out_path: output video path
    """
    w, h = size
    total_frames = int(audio_duration_sec(audio_file) * fps)
    if not images:
        raise ValueError("No images provided for montage.")
    # Equal segment per image
    seg_frames = max(1, total_frames // len(images))

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # will be recompressed later via ffmpeg
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    writer = cv2.VideoWriter(out_path, fourcc, fps, (w, h))

    for idx, img_path in enumerate(images):
        img = cv2.imread(img_path)
        if img is None:
            # fallback: blank frame
            img = np.zeros((h,w,3), dtype=np.uint8)
        ih, iw = img.shape[:2]
        current_aspect = iw / ih
        target_aspect = w / h

        for f in range(seg_frames):
            t = f / max(1, seg_frames-1)
            # zoom from 1.05 -> 1.15
            zoom = 1.05 + 0.1 * t

            # Calculate crop dimensions maintaining aspect ratio to cover the target
            if current_aspect > target_aspect:
                # Image is wider than target aspect ratio, scale to target height and crop width
                crop_h_scaled = int(ih / zoom)
                crop_w_scaled = int(crop_h_scaled * target_aspect)
            else:
                # Image is taller than target aspect ratio, scale to target width and crop height
                crop_w_scaled = int(iw / zoom)
                crop_h_scaled = int(crop_w_scaled / target_aspect)

            # Ensure crop dimensions are within image bounds
            crop_w = min(crop_w_scaled, iw)
            crop_h = min(crop_h_scaled, ih)

            # pan from center slightly to right/down
            cx = int((iw - crop_w) * (0.5 + 0.1 * t))
            cy = int((ih - crop_h) * (0.5 + 0.1 * t))
            cx = max(0, min(cx, iw - crop_w))
            cy = max(0, min(cy, ih - crop_h))

            crop = img[cy:cy+crop_h, cx:cx+crop_w]
            frame = cv2.resize(crop, (w, h), interpolation=cv2.INTER_CUBIC)
            writer.write(frame)

    # pad last frame if needed
    written = seg_frames * len(images)
    for _ in range(max(0, total_frames - written)):
        writer.write(frame)

    out_path_audio = out_path.strip().split(".mp4")[0]+"_audio.mp4"   
    writer.release()
    cmd = ["ffmpeg", "-i", out_path, "-i", audio_file, "-c:v", "copy", "-c:a", "aac", "-shortest", out_path_audio]
    subprocess.run(cmd, check=True)
    os.remove(out_path)
    os.rename(out_path_audio, out_path)   
    return out_path

def generate_video(images, output, prompt, audio, size, model):
    if model == "montage":
        return ken_burns_clip(images, output, audio, size=size)
        
    elif model != "montage":
        repo = os.getenv("MULTITALK_PATH", None)
        if repo and os.path.exists(repo):
            # For LTX-Video frame count needs to be 17 + multiple of 8
            frames = str(17 + 8 * math.ceil((math.ceil(audio_duration_sec(audio) * 30) - 17) / 8)) if audio_duration_sec(audio) > 0.0 else 17
            parent_dir = os.getcwd()
            if not (os.path.isabs(output) and os.path.isabs(images[0])):
                images[0] = os.path.join(parent_dir, images[0])
                output = os.path.join(parent_dir, output)
            cmd = [sys.executable,
                    os.path.join(repo, "inference.py"),
                    "--model", model,
                    "--image", images[0],
                    "--output", output,
                    "--prompt", prompt,
                    "--frames", frames,
                    "--resolution", size,
                    "--steps", "7",
                    "--sliding-window-size", "257",
                    "--guidance", "3.0",
                    "--memory-profile", "5",
                    "--quant", "int8"
                ]

            subprocess.run(cmd, cwd=repo, check=True)
            out_path_audio = output.strip().split(".mp4")[0]+"_audio.mp4"   
            cmd = [
                        "ffmpeg", "-y",
                        "-i", output,
                        "-i", audio,
                        "-c:v", "copy",
                        "-c:a", "aac", "-b:a", "192k",
                        "-map", "0:v:0",
                        "-map", "1:a:0",
                        "-shortest",
                        out_path_audio
                    ]
            subprocess.run(cmd, check=True)
            os.remove(output)
            os.rename(out_path_audio, output)   
            return output
        else:
            raise RuntimeError("MULTITALLK_PATH not set or invalid. Install MultiTalk and set environment variable.")