import os
import sys
import subprocess
from app.video.assemble import audio_duration_sec

def generate_talking_head(portrait_path, audio, out_mp4, prompt, engine="sadtalker", target_width=960, target_height=540):
    """
    Generate talking head with standardized output resolution to prevent stretching.
    """
    os.makedirs(os.path.dirname(out_mp4), exist_ok=True)

    if engine.lower() == "wav2lip":
        repo = os.getenv("WAV2LIP_PATH", None)
        if repo and os.path.exists(repo):
            # Generate raw output first
            raw_output = out_mp4.replace('.mp4', '_raw.mp4')
            
            # Example call (this depends on your local Wav2Lip setup)
            # Adjust paths/flags to match your cloned repo.
            cmd = [sys.executable, os.path.join(repo, "inference.py"),
                "--checkpoint_path", os.path.join(repo, "checkpoints", "Wav2Lip-SD-GAN.pt"),
                "--face", portrait_path,
                "--audio", audio[0],
                "--outfile", raw_output
            ]
            subprocess.run(cmd, check=True)
            
            # Post-process to standardize resolution and prevent stretching
            subprocess.run([
                "ffmpeg", "-y", "-i", raw_output,
                "-vf", f"scale={target_width}:{target_height}:force_original_aspect_ratio=increase,crop={target_width}:{target_height},pad=width=ceil(iw/2)*2:height=ceil(ih/2)*2",
                "-c:v", "libx264", "-pix_fmt", "yuv420p", "-c:a", "aac",
                out_mp4
            ], check=True)
            
            # Clean up raw output
            if os.path.exists(raw_output):
                os.remove(raw_output)
            return out_mp4
        else:
            raise RuntimeError("WAV2LIP_PATH not set or invalid. Install Wav2Lip and set environment variable.")
            
    elif engine.lower() == "sadtalker":
        repo = os.getenv("SADTALKER_PATH", None)
        if repo and os.path.exists(repo):
            # Generate raw output first
            raw_output = out_mp4.replace('.mp4', '_raw.mp4')
            
            # Example call (adjust to your local SadTalker CLI)
            cmd = [sys.executable, os.path.join(repo, "inference.py"),
                "--source_image", portrait_path,
                "--driven_audio", audio[0],
                "--checkpoint_dir", "third_party/SadTalker/checkpoints/",
                "--bfm_folder", "third_party/SadTalker/checkpoints/BFM_Fitting",
                "--result_dir", os.path.dirname(out_mp4),
                "--enhancer", "gfpgan",
                "--still",
                "--preprocess", "full",
                "--expression_scale", "1.0"
            ]
            subprocess.run(cmd, check=True)
            
            # Find output mp4 under result_dir, move/rename to raw_output
            # (This part needs to be adapted based on SadTalker's actual output naming)
            # For now, assuming it outputs to the result_dir with a predictable name
            result_files = [f for f in os.listdir(os.path.dirname(out_mp4)) if f.endswith('.mp4')]
            if result_files:
                temp_result = os.path.join(os.path.dirname(out_mp4), result_files[0])
                if temp_result != raw_output:
                    os.rename(temp_result, raw_output)
            
            # Post-process to standardize resolution
            if os.path.exists(raw_output):
                subprocess.run([
                    "ffmpeg", "-y", "-i", raw_output,
                    "-vf", f"scale={target_width}:{target_height}:force_original_aspect_ratio=increase,crop={target_width}:{target_height},pad=width=ceil(iw/2)*2:height=ceil(ih/2)*2",
                    "-c:v", "libx264", "-pix_fmt", "yuv420p", "-c:a", "aac",
                    out_mp4
                ], check=True)
                
                # Clean up raw output
                os.remove(raw_output)
            
            return out_mp4
        else:
            raise RuntimeError("SADTALKER_PATH not set or invalid. Install SadTalker and set environment variable.")

    elif engine.lower()=="multitalk":
        repo = os.getenv("MULTITALK_PATH")
        parent_dir = os.getcwd()
        if not (os.path.isabs(portrait_path) and os.path.isabs(audio[0])):
            portrait_path = os.path.join(parent_dir, portrait_path)
            audio[0] = os.path.join(parent_dir, audio[0])
            audio[1] = os.path.join(parent_dir, audio[1]) if len(audio)>1 else ""
            out_mp4 = os.path.join(parent_dir, out_mp4)
        if repo and os.path.exists(repo):
            audio_durations = [audio_duration_sec(a) for a in audio]
            frames = str(int(sum(audio_durations) * 25))
            cmd = [sys.executable,
                    os.path.join(repo, "inference.py"),
                    "--model", "multitalk",
                    "--image", portrait_path,
                    "--audio1", audio[0],
                    "--audio2", audio[1],
                    "--output", out_mp4,
                    "--prompt", prompt,
                    "--resolution", f"{target_width}x{target_height}",
                    "--frames", frames,
                    "--steps", "30",
                    "--speaker-mode", "sequential",
                    "--clean-audio",
                    "--sliding-window-size", "257",
                    "--memory-profile", "5",
                    "--quant", "int8"
            ]
            subprocess.run(cmd, cwd=repo, check=True)
            return out_mp4
        else:
            raise RuntimeError("MULTITALK_PATH not set or invalid. Install MultiTalk and set environment variable.")

    elif engine.lower()=="vace_multitalk":
        repo = os.getenv("MULTITALK_PATH")
        parent_dir = os.getcwd()
        if not (os.path.isabs(portrait_path) and os.path.isabs(audio[0])):
            portrait_path = os.path.join(parent_dir, portrait_path)
            audio[0] = os.path.join(parent_dir, audio[0])
            audio[1] = os.path.join(parent_dir, audio[1]) if len(audio)>1 else ""
            out_mp4 = os.path.join(parent_dir, out_mp4)
        if repo and os.path.exists(repo):
            audio_durations = [audio_duration_sec(a) for a in audio]
            frames = str(int(sum(audio_durations) * 25))
            cmd = [sys.executable,
                    os.path.join(repo, "inference.py"),
                    "--model", "vace_multitalk_14B",
                    "--image", portrait_path,
                    "--audio1", audio[0],
                    "--audio2", audio[1],
                    "--output", out_mp4,
                    "--prompt", prompt,
                    "--resolution", f"{target_width}x{target_height}",
                    "--frames", frames,
                    "--steps", "8",
                    "--speaker-mode", "sequential",
                    "--clean-audio",
                    "--reference-images", portrait_path,
                    "--reference-image-type", "inject-people",
                    "--remove-background-reference", "0",
                    "--sliding-window-size", "257",
                    "--memory-profile", "5",
                    "--quant", "int8"
            ]
            subprocess.run(cmd, cwd=repo, check=True)
            return out_mp4
        else:
            raise RuntimeError("MULTITALLK_PATH not set or invalid. Install MultiTalk and set environment variable.")

    else:
        raise ValueError(f"Unknown talking head engine: {engine}")
