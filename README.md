# AI Video Generation

**MVP features**: <PERSON><PERSON><PERSON> spec -> local TTS -> narration montage (<PERSON>) or *stub* talking-head (Wav2Lip/SadTalker) -> subtitles/watermark -> compression (2–5 MB).

## Quick start (macOS)
```bash
cd AI-Video-generation
# (Optional) Install/prepare TTS:
# - Piper: install 'piper' binary, download a voice model (e.g., en_US-amy-low.onnx), set env PIPER_VOICE or put voice path in spec.
# macOS fallback uses 'say' if <PERSON> isn't set.
```

## Conda Environment Setup
Run the script
```bash
cd AI-Video-generation
source scripts/setup_macos.sh
```
### Suno's Bark Setup
You can also use Suno's Bark instead of Piper-TTS for speech generation. To install Bark from their GitHub repo:
```python
pip install --no-deps git+https://github.com/suno-ai/bark.git
#--no-deps will prevent pip from upgrading PyTorch version.
```
## Flask UI
```bash
export FLASK_APP=web/server.py
python web/server.py
# open http://127.0.0.1:5000
```

## Spec format
See `examples/exampele1.json`. Provide per-scene `mode: "narration"` or `"talking_head"`.

## Compression targets
The pipeline uses ffmpeg 2-pass to aim for 2–5 MB at 15–30s with 540p @ 25 fps.
Adjust `target_size_mb` in the spec.

## Notes
- Keep everything local. No cloud calls.
- Add watermark and avoid voice cloning of real people without consent.
- For better voice quality, integrate Coqui TTS instead of Piper/macOS 'say'.

### ffmpeg
Install and add to PATH. Easiest via Chocolatey:
```powershell
choco install ffmpeg
```

### Piper TTS
Download a voice and set env var:
```powershell
# download (defaults to en_US-amy-low)
.\.venv\Scripts\python scripts\download_piper_voice.py
# set env so tts.py can find it
setx PIPER_VOICE "%CD%\models\piper\en_US-amy-low.onnx"
```
### Run generation
```powershell
conda activate aivideos
python generate.py --spec examples\spec_sample.json --out runs\demo\out.mp4 --workdir runs\demo
```

### Flask UI
```powershell
python web\server.py
# open http://127.0.0.1:5000
```
