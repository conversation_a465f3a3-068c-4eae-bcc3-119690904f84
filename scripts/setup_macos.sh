#!/bin/bash
set -e

# Create installation directory
mkdir -p ~/miniconda3

# Download Miniconda installer
curl -L https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-arm64.sh -o ~/miniconda3/miniconda.sh

# Run the installer silently (-b accepts license, -u updates existing, -p sets prefix)
bash ~/miniconda3/miniconda.sh -b -u -p ~/miniconda3

# Remove installer script
rm ~/miniconda3/miniconda.sh

# Activate conda
source ~/miniconda3/bin/activate

# Initialize conda for all shells
conda init --all

# Auto-confirm environment creation with Python 3.10.18
conda create -n aivideos python=3.10.18 -y

# Activate the new environment
conda activate aivideos

# Install packages
pip install voxcpm
pip install -r requirements.txt
pip install torch==2.7.0 torchaudio torchvision --force-reinstall

echo "Setup complete! Environment 'aivideos' is active with VoxCPM and requirements installed."
