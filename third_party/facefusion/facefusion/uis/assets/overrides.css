:root:root:root:root .gradio-container
{
	overflow: unset;
}

:root:root:root:root main
{
	max-width: 110em;
}

:root:root:root:root input[type="number"]
{
	appearance: textfield;
	border-radius: unset;
	text-align: center;
	order: 1;
	padding: unset
}

:root:root:root:root input[type="number"]::-webkit-inner-spin-button
{
	appearance: none;
}

:root:root:root:root input[type="number"]:focus
{
	outline: unset;
}

:root:root:root:root .reset-button
{
	background: var(--background-fill-secondary);
	border: unset;
	font-size: unset;
	padding: unset;
}

:root:root:root:root [type="checkbox"],
:root:root:root:root [type="radio"]
{
	border-radius: 50%;
	height: 1.125rem;
	width: 1.125rem;
}

:root:root:root:root input[type="range"]
{
	background: transparent;
}

:root:root:root:root input[type="range"]::-moz-range-thumb,
:root:root:root:root input[type="range"]::-webkit-slider-thumb
{
	background: var(--neutral-300);
	box-shadow: unset;
	border-radius: 50%;
	height: 1.125rem;
	width: 1.125rem;
}

:root:root:root:root .thumbnail-item
{
	border: unset;
	box-shadow: unset;
}

:root:root:root:root .grid-wrap.fixed-height
{
	min-height: unset;
}

:root:root:root:root .box-face-selector .empty,
:root:root:root:root .box-face-selector .gallery-container
{
	min-height: 7.375rem;
}

:root:root:root:root .tab-wrapper
{
	padding: 0 0.625rem;
}

:root:root:root:root .tab-container
{
	gap: 0.5em;
}

:root:root:root:root .tab-container button
{
	background: unset;
	border-bottom: 0.125rem solid;
}

:root:root:root:root .tab-container button.selected
{
	color: var(--primary-500)
}

:root:root:root:root .toast-body
{
	background: white;
	color: var(--primary-500);
	border: unset;
	border-radius: unset;
}

:root:root:root:root .dark .toast-body
{
	background: var(--neutral-900);
	color: var(--primary-600);
}

:root:root:root:root .toast-icon,
:root:root:root:root .toast-title,
:root:root:root:root .toast-text,
:root:root:root:root .toast-close
{
	color: unset;
}

:root:root:root:root .toast-body .timer
{
	background: currentColor;
}

:root:root:root:root .slider_input_container > span,
:root:root:root:root .feather-upload,
:root:root:root:root footer
{
	display: none;
}

:root:root:root:root .image-frame
{
	width: 100%;
}

:root:root:root:root .image-frame > img
{
	object-fit: cover;
}

:root:root:root:root .image-preview.is-landscape
{
	position: sticky;
	top: 0;
	z-index: 100;
}

:root:root:root:root .block .error
{
	border: 0.125rem solid;
	padding: 0.375rem 0.75rem;
	font-size: 0.75rem;
	text-transform: uppercase;
}
