{"model": {"name": "Wan2.2 Vace Lightning 3 Phases 14B", "architecture": "vace_14B", "modules": ["vace_14B"], "description": "This finetune uses the Lightning 4 steps Loras Accelerator for Wan 2.2 but extend them to 8 steps in order to insert a CFG phase before the 2 accelerated phases with no Guidance. The ultimate goal is reduce the slow motion effect of these Loras Accelerators.", "URLs": "t2v_2_2", "URLs2": "t2v_2_2", "loras": ["https://huggingface.co/DeepBeepMeep/Wan2.2/resolve/main/loras_accelerators/Wan2.2-Lightning_T2V-v1.1-A14B-4steps-lora_HIGH_fp16.safetensors", "https://huggingface.co/DeepBeepMeep/Wan2.2/resolve/main/loras_accelerators/Wan2.2-Lightning_T2V-v1.1-A14B-4steps-lora_LOW_fp16.safetensors"], "loras_multipliers": ["0;1;0", "0;0;1"], "lock_guidance_phases": true, "group": "wan2_2"}, "num_inference_steps": 8, "guidance_phases": 3, "guidance_scale": 3.5, "guidance2_scale": 1, "guidance3_scale": 1, "switch_threshold": 965, "switch_threshold2": 800, "model_switch_phase": 2, "flow_shift": 3, "sample_solver": "euler"}