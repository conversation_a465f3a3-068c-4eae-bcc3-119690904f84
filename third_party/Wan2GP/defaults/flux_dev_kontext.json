{"model": {"name": "Flux 1 Dev Kontext 12B", "architecture": "flux", "description": "FLUX.1 Kontext is a 12 billion parameter rectified flow transformer capable of editing images based on instructions stored in the Prompt. Please be aware that Flux Kontext is picky on the resolution of the input image and the output dimensions may not match the dimensions of the input image.", "URLs": ["https://huggingface.co/DeepBeepMeep/Flux/resolve/main/flux1_kontext_dev_bf16.safetensors", "https://huggingface.co/DeepBeepMeep/Flux/resolve/main/flux1_kontext_dev_quanto_bf16_int8.safetensors"], "flux-model": "flux-dev-kontext"}, "prompt": "add a hat", "resolution": "1280x720", "batch_size": 1}