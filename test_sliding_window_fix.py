#!/usr/bin/env python3
"""
Test script to verify the sliding window bug fix.

This script tests the scenario where num_frames > sliding_window_size
to ensure that all windows are processed correctly.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the Wan2GP directory to the path
wan2gp_path = Path(__file__).parent / "third_party" / "Wan2GP"
sys.path.insert(0, str(wan2gp_path))

def test_sliding_window_fix():
    """Test the sliding window fix with a mock scenario."""
    
    print("Testing sliding window fix...")
    
    # Test parameters that would trigger sliding window
    test_frames = 200  # More than typical sliding window size of 129
    sliding_window_size = 129
    
    print(f"Test scenario:")
    print(f"  - Requested frames: {test_frames}")
    print(f"  - Sliding window size: {sliding_window_size}")
    print(f"  - Expected windows: {(test_frames - 1) // (sliding_window_size - 8) + 1}")  # Rough calculation
    
    # Import the necessary modules
    try:
        from inference import (
            get_available_models, 
            get_model_capabilities,
            create_send_cmd_callback,
            create_state_object,
            create_generation_parameters,
            create_task_object,
            generate_video_cli
        )
        print("✓ Successfully imported inference modules")
    except ImportError as e:
        print(f"✗ Failed to import inference modules: {e}")
        return False
    
    # Check available models
    try:
        models = get_available_models()
        if not models:
            print("✗ No models found - cannot test without models")
            return False
        
        # Try to find a simple model for testing
        test_model = None
        for model_name in models:
            capabilities = get_model_capabilities(model_name)
            # Look for a model that doesn't require audio or complex setup
            if not capabilities.get("requires_audio", False) and not capabilities.get("requires_image", False):
                test_model = model_name
                break
        
        if not test_model:
            print("✗ No suitable test model found")
            return False
            
        print(f"✓ Using test model: {test_model}")
        
    except Exception as e:
        print(f"✗ Error checking models: {e}")
        return False
    
    # Test the callback system
    try:
        send_cmd = create_send_cmd_callback()
        
        # Test that the callback handles different commands
        test_commands = ["progress", "status", "output", "error"]
        for cmd in test_commands:
            send_cmd(cmd, f"test_{cmd}")
        
        print("✓ Callback system working")
        
    except Exception as e:
        print(f"✗ Callback system error: {e}")
        return False
    
    # Test state object creation
    try:
        state = create_state_object(test_model, f"{test_model}.safetensors")
        
        # Verify sliding window parameters are set correctly
        gen = state.get("gen", {})
        if "sliding_window" not in gen:
            print("✗ State object missing sliding window support")
            return False
            
        print("✓ State object created successfully")
        
    except Exception as e:
        print(f"✗ State object creation error: {e}")
        return False
    
    print("\n✓ All basic tests passed!")
    print("\nNote: Full integration test requires actual model files and GPU resources.")
    print("The fix should now properly handle sliding window generation in CLI mode.")
    
    return True

def test_window_calculation():
    """Test the sliding window calculation logic."""
    
    print("\nTesting sliding window calculations...")
    
    # Import the calculation function
    try:
        sys.path.insert(0, str(Path(__file__).parent / "third_party" / "Wan2GP"))
        from wgp import compute_sliding_window_no
        
        # Test various scenarios
        test_cases = [
            (200, 129, 0, 8),   # 200 frames, 129 window, 0 discard, 8 reuse
            (300, 129, 0, 8),   # 300 frames, 129 window, 0 discard, 8 reuse  
            (150, 129, 0, 8),   # 150 frames, 129 window, 0 discard, 8 reuse
        ]
        
        for frames, window_size, discard, reuse in test_cases:
            windows = compute_sliding_window_no(frames, window_size, discard, reuse)
            print(f"  Frames: {frames}, Window: {window_size} -> {windows} windows")
            
            if windows <= 1 and frames > window_size:
                print(f"    ✗ Expected multiple windows for {frames} frames")
                return False
        
        print("✓ Window calculations look correct")
        return True
        
    except Exception as e:
        print(f"✗ Window calculation test error: {e}")
        return False

if __name__ == "__main__":
    print("Sliding Window Bug Fix Test")
    print("=" * 40)
    
    success = True
    
    # Run basic tests
    if not test_sliding_window_fix():
        success = False
    
    # Run window calculation tests
    if not test_window_calculation():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed! The sliding window fix should work correctly.")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    
    sys.exit(0 if success else 1)
